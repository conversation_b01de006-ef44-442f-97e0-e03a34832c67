import { useState, useEffect, useCallback } from 'react';
import { supabase } from '../lib/supabase';
import { useAuth } from './useAuth';
import { subMonths } from 'date-fns';
import { Alert } from 'react-native';
import {
  initialWallets,
  initialTransactions,
  initialBudgets,
  initialCategories,
  FREE_TIER_LIMITS,
  CATEGORY_ICONS,
} from '../lib/constants';
import type {
  Wallet,
  Transaction,
  Budget,
  Category,
  CategoryInfo,
  Subscription,
  TransactionFormData,
  WalletFormData,
} from '../lib/types';

type UserData = {
  wallets: Wallet[];
  transactions: Transaction[];
  budgets: Budget[];
  categories: CategoryInfo[];
  subscription: Subscription;
};

export function useTransactions() {
  const { user, isSignedIn } = useAuth();
  const [wallets, setWallets] = useState<Wallet[]>(initialWallets);
  const [transactions, setTransactions] = useState<Transaction[]>(initialTransactions);
  const [budgets, setBudgets] = useState<Budget[]>(initialBudgets);
  const [categories, setCategories] = useState<Category[]>([]);
  const [subscription, setSubscription] = useState<Subscription>({ tier: 'free' });
  const [isInitialized, setIsInitialized] = useState(false);
  const [loading, setLoading] = useState(false);

  // Convert CategoryInfo to Category with icons
  const convertCategoriesToWithIcons = useCallback((categoryInfos: CategoryInfo[]): Category[] => {
    return categoryInfos.map(c => ({
      ...c,
      icon: CATEGORY_ICONS.find(icon => icon.name === c.iconName)?.icon || CATEGORY_ICONS[0].icon,
    }));
  }, []);

  // Load data from Supabase
  const loadFromSupabase = useCallback(async (userId: string): Promise<UserData> => {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('data')
        .eq('id', userId)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          // Profile doesn't exist, create with default data
          const defaultData = getDefaultData();
          await saveToSupabase(userId, defaultData);
          return defaultData;
        }
        console.warn('Error loading from Supabase:', error);
        return getDefaultData();
      }

      if (data?.data) {
        const userData = data.data as UserData;
        // Re-hydrate dates for transactions
        if (userData.transactions) {
          userData.transactions = userData.transactions.map(t => ({
            ...t,
            date: new Date(t.date)
          }));
        }
        return {
          wallets: userData.wallets || initialWallets,
          transactions: userData.transactions || initialTransactions,
          budgets: userData.budgets || initialBudgets,
          categories: userData.categories || initialCategories,
          subscription: userData.subscription || { tier: 'free' },
        };
      }
    } catch (error) {
      console.warn('Error parsing Supabase data:', error);
    }

    return getDefaultData();
  }, []);

  // Save data to Supabase
  const saveToSupabase = useCallback(async (userId: string, data: UserData) => {
    try {
      const { error } = await supabase
        .from('profiles')
        .upsert({
          id: userId,
          data: data
        });

      if (error) {
        console.warn('Error saving to Supabase:', error);
        Alert.alert('Sync Error', 'Failed to save data to cloud. Your changes are saved locally.');
      }
    } catch (error) {
      console.warn('Error saving to Supabase:', error);
    }
  }, []);

  // Get default data
  const getDefaultData = useCallback((): UserData => {
    return {
      wallets: initialWallets,
      transactions: initialTransactions,
      budgets: initialBudgets,
      categories: initialCategories,
      subscription: { tier: 'free' },
    };
  }, []);

  // Save all data
  const saveAllData = useCallback(async () => {
    if (!user?.id) return;

    const userData: UserData = {
      wallets,
      transactions,
      budgets,
      categories: categories.map(c => ({
        value: c.value,
        label: c.label,
        iconName: c.iconName,
        type: c.type
      })),
      subscription,
    };

    await saveToSupabase(user.id, userData);
  }, [user?.id, wallets, transactions, budgets, categories, subscription, saveToSupabase]);

  // Initialize data when user signs in
  useEffect(() => {
    const initializeData = async () => {
      if (!user?.id) {
        setIsInitialized(false);
        return;
      }

      setLoading(true);
      try {
        const userData = await loadFromSupabase(user.id);
        
        setWallets(userData.wallets);
        setTransactions(userData.transactions);
        setBudgets(userData.budgets);
        setCategories(convertCategoriesToWithIcons(userData.categories));
        setSubscription(userData.subscription);

        // Enforce free tier data retention
        if (userData.subscription.tier === 'free') {
          const twoMonthsAgo = subMonths(new Date(), FREE_TIER_LIMITS.historyMonths);
          const filteredTransactions = userData.transactions.filter(t => t.date >= twoMonthsAgo);
          if (filteredTransactions.length < userData.transactions.length) {
            setTransactions(filteredTransactions);
          }
        }

        setIsInitialized(true);
      } catch (error) {
        console.error('Error initializing data:', error);
        setIsInitialized(true);
      } finally {
        setLoading(false);
      }
    };

    initializeData();
  }, [user?.id, loadFromSupabase, convertCategoriesToWithIcons]);

  // Auto-save when data changes
  useEffect(() => {
    if (isInitialized && user?.id) {
      saveAllData();
    }
  }, [wallets, transactions, budgets, categories, subscription, isInitialized, user?.id, saveAllData]);

  // Update wallet balance
  const updateWalletBalance = useCallback((walletId: string, amount: number, type: 'add' | 'subtract') => {
    setWallets(prev => prev.map(wallet => {
      if (wallet.id === walletId) {
        const newBalance = type === 'add' ? wallet.balance + amount : wallet.balance - amount;
        return { ...wallet, balance: newBalance };
      }
      return wallet;
    }));
  }, []);

  // Add transaction
  const addTransaction = useCallback(async (data: TransactionFormData) => {
    const newTransaction: Transaction = {
      id: `tx-${Date.now()}`,
      type: data.type,
      amount: data.amount,
      category: data.category,
      description: data.description,
      walletId: data.walletId,
      toWalletId: data.toWalletId,
      date: new Date(),
    };

    setTransactions(prev => [newTransaction, ...prev]);

    // Update wallet balances
    if (data.type === 'transfer') {
      updateWalletBalance(data.walletId, data.amount, 'subtract');
      if (data.toWalletId) {
        updateWalletBalance(data.toWalletId, data.amount, 'add');
      }
    } else {
      const balanceUpdateType = data.type === 'income' ? 'add' : 'subtract';
      updateWalletBalance(data.walletId, data.amount, balanceUpdateType);
    }
  }, [updateWalletBalance]);

  // Edit transaction
  const editTransaction = useCallback(async (id: string, data: TransactionFormData) => {
    const oldTransaction = transactions.find(t => t.id === id);
    if (!oldTransaction) return;

    // Revert old transaction's wallet balance changes
    if (oldTransaction.type === 'transfer') {
      updateWalletBalance(oldTransaction.walletId, oldTransaction.amount, 'add');
      if (oldTransaction.toWalletId) {
        updateWalletBalance(oldTransaction.toWalletId, oldTransaction.amount, 'subtract');
      }
    } else {
      const revertType = oldTransaction.type === 'income' ? 'subtract' : 'add';
      updateWalletBalance(oldTransaction.walletId, oldTransaction.amount, revertType);
    }

    // Apply new transaction's wallet balance changes
    if (data.type === 'transfer') {
      updateWalletBalance(data.walletId, data.amount, 'subtract');
      if (data.toWalletId) {
        updateWalletBalance(data.toWalletId, data.amount, 'add');
      }
    } else {
      const balanceUpdateType = data.type === 'income' ? 'add' : 'subtract';
      updateWalletBalance(data.walletId, data.amount, balanceUpdateType);
    }

    // Update transaction
    setTransactions(prev => prev.map(t =>
      t.id === id
        ? { ...t, ...data }
        : t
    ));
  }, [transactions, updateWalletBalance]);

  // Delete transaction
  const deleteTransaction = useCallback(async (id: string) => {
    const transaction = transactions.find(t => t.id === id);
    if (!transaction) return;

    // Revert wallet balance changes
    if (transaction.type === 'transfer') {
      updateWalletBalance(transaction.walletId, transaction.amount, 'add');
      if (transaction.toWalletId) {
        updateWalletBalance(transaction.toWalletId, transaction.amount, 'subtract');
      }
    } else {
      const revertType = transaction.type === 'income' ? 'subtract' : 'add';
      updateWalletBalance(transaction.walletId, transaction.amount, revertType);
    }

    setTransactions(prev => prev.filter(t => t.id !== id));
  }, [transactions, updateWalletBalance]);

  // Add wallet
  const addWallet = useCallback(async (data: WalletFormData) => {
    if (subscription.tier === 'free' && wallets.length >= FREE_TIER_LIMITS.wallets) {
      Alert.alert('Upgrade to Premium', 'You\'ve reached the maximum number of wallets for the free tier.');
      return;
    }

    const newWallet: Wallet = {
      id: `wallet-${Date.now()}`,
      name: data.name,
      balance: data.initialBalance,
      icon: data.icon,
      currency: 'USD',
    };

    setWallets(prev => [...prev, newWallet]);
  }, [subscription.tier, wallets.length]);

  // Edit wallet
  const editWallet = useCallback(async (id: string, data: WalletFormData) => {
    setWallets(prev => prev.map(w =>
      w.id === id
        ? { ...w, name: data.name, icon: data.icon, balance: data.initialBalance }
        : w
    ));
  }, []);

  // Delete wallet
  const deleteWallet = useCallback(async (id: string) => {
    // Check if wallet has transactions
    const hasTransactions = transactions.some(t => t.walletId === id || t.toWalletId === id);
    if (hasTransactions) {
      Alert.alert('Cannot Delete', 'This wallet has transactions. Please delete all transactions first.');
      return;
    }

    setWallets(prev => prev.filter(w => w.id !== id));
  }, [transactions]);

  // Set budgets
  const setBudgetsData = useCallback(async (newBudgets: Budget[]) => {
    setBudgets(newBudgets);
  }, []);

  // Add category
  const addCategory = useCallback(async (data: CategoryInfo) => {
    if (subscription.tier === 'free' && categories.length >= FREE_TIER_LIMITS.categories) {
      Alert.alert('Upgrade to Premium', 'You\'ve reached the maximum number of categories for the free tier.');
      return;
    }

    const newCategory: Category = {
      ...data,
      icon: CATEGORY_ICONS.find(icon => icon.name === data.iconName)?.icon || CATEGORY_ICONS[0].icon,
    };

    setCategories(prev => [...prev, newCategory]);
  }, [subscription.tier, categories.length]);

  // Edit category
  const editCategory = useCallback(async (value: string, data: CategoryInfo) => {
    setCategories(prev => prev.map(c =>
      c.value === value
        ? {
            ...data,
            icon: CATEGORY_ICONS.find(icon => icon.name === data.iconName)?.icon || CATEGORY_ICONS[0].icon,
          }
        : c
    ));
  }, []);

  // Delete category
  const deleteCategory = useCallback(async (value: string) => {
    // Check if category has transactions
    const hasTransactions = transactions.some(t => t.category === value);
    if (hasTransactions) {
      Alert.alert('Cannot Delete', 'This category has transactions. Please delete all transactions first.');
      return;
    }

    setCategories(prev => prev.filter(c => c.value !== value));
  }, [transactions]);

  // Upgrade to premium
  const upgradeToPremium = useCallback(async () => {
    setSubscription({ tier: 'premium' });
  }, []);

  return {
    wallets,
    transactions,
    budgets,
    categories,
    subscription,
    isInitialized,
    loading,
    // Actions
    addTransaction,
    editTransaction,
    deleteTransaction,
    addWallet,
    editWallet,
    deleteWallet,
    setBudgets: setBudgetsData,
    addCategory,
    editCategory,
    deleteCategory,
    upgradeToPremium,
  };
}
