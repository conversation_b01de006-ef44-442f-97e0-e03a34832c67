import React, { useState, useMemo } from 'react';
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
  StyleSheet,
  Alert,
  ScrollView,
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { formatCurrency, formatTransactionDate, getTransactionTypeColor } from '../lib/utils';
import { useTransactions } from '../hooks/useTransactions';
import type { Transaction, Wallet, TransactionType } from '../lib/types';

interface TransactionListProps {
  transactions: Transaction[];
  wallets: Wallet[];
  onEdit: (transaction: Transaction) => void;
  showHeader?: boolean;
  showFilters?: boolean;
}

interface TransactionItemProps {
  transaction: Transaction;
  wallets: Wallet[];
  onEdit: (transaction: Transaction) => void;
  onDelete: (id: string) => void;
}

function TransactionItem({ transaction, wallets, onEdit, onDelete }: TransactionItemProps) {
  const { categories } = useTransactions();
  
  const wallet = wallets.find(w => w.id === transaction.walletId);
  const toWallet = transaction.toWalletId 
    ? wallets.find(w => w.id === transaction.toWalletId)
    : null;
  
  const category = categories.find(c => c.value === transaction.category);
  const typeColor = getTransactionTypeColor(transaction.type);

  const handleLongPress = () => {
    Alert.alert(
      'Transaction Options',
      'What would you like to do?',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Edit', onPress: () => onEdit(transaction) },
        { 
          text: 'Delete', 
          style: 'destructive',
          onPress: () => {
            Alert.alert(
              'Delete Transaction',
              'Are you sure you want to delete this transaction?',
              [
                { text: 'Cancel', style: 'cancel' },
                { text: 'Delete', style: 'destructive', onPress: () => onDelete(transaction.id) },
              ]
            );
          }
        },
      ]
    );
  };

  const getTransactionDescription = () => {
    if (transaction.type === 'transfer') {
      return `${wallet?.name} → ${toWallet?.name}`;
    }
    return transaction.description || category?.label || 'Transaction';
  };

  const getTransactionIcon = () => {
    if (transaction.type === 'transfer') {
      return 'swap-horiz';
    }
    return transaction.type === 'income' ? 'trending-up' : 'trending-down';
  };

  return (
    <TouchableOpacity
      style={styles.transactionItem}
      onPress={() => onEdit(transaction)}
      onLongPress={handleLongPress}
      activeOpacity={0.7}
    >
      <View style={styles.transactionLeft}>
        <View style={[styles.iconContainer, { backgroundColor: `${typeColor}20` }]}>
          <MaterialIcons 
            name={getTransactionIcon()} 
            size={20} 
            color={typeColor} 
          />
        </View>
        <View style={styles.transactionInfo}>
          <Text style={styles.transactionDescription} numberOfLines={1}>
            {getTransactionDescription()}
          </Text>
          <View style={styles.transactionMeta}>
            <Text style={styles.transactionDate}>
              {formatTransactionDate(transaction.date)}
            </Text>
            {transaction.type !== 'transfer' && category && (
              <>
                <Text style={styles.metaSeparator}>•</Text>
                <Text style={styles.transactionCategory}>{category.label}</Text>
              </>
            )}
          </View>
        </View>
      </View>
      <View style={styles.transactionRight}>
        <Text style={[styles.transactionAmount, { color: typeColor }]}>
          {transaction.type === 'expense' ? '-' : '+'}
          {formatCurrency(transaction.amount)}
        </Text>
        <Text style={styles.walletName} numberOfLines={1}>
          {wallet?.name}
        </Text>
      </View>
    </TouchableOpacity>
  );
}

export default function TransactionList({
  transactions,
  wallets,
  onEdit,
  showHeader = true,
  showFilters = false,
}: TransactionListProps) {
  const { deleteTransaction, categories } = useTransactions();
  const [selectedFilter, setSelectedFilter] = useState<TransactionType | 'all'>('all');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');

  const filteredTransactions = useMemo(() => {
    let filtered = transactions;

    // Filter by type
    if (selectedFilter !== 'all') {
      filtered = filtered.filter(t => t.type === selectedFilter);
    }

    // Filter by category
    if (selectedCategory !== 'all') {
      filtered = filtered.filter(t => t.category === selectedCategory);
    }

    return filtered.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
  }, [transactions, selectedFilter, selectedCategory]);

  const handleDelete = async (id: string) => {
    await deleteTransaction(id);
  };

  const renderItem = ({ item }: { item: Transaction }) => (
    <TransactionItem
      transaction={item}
      wallets={wallets}
      onEdit={onEdit}
      onDelete={handleDelete}
    />
  );

  const renderFilters = () => {
    if (!showFilters) return null;

    const typeFilters = [
      { value: 'all', label: 'All', icon: 'list' },
      { value: 'income', label: 'Income', icon: 'trending-up' },
      { value: 'expense', label: 'Expense', icon: 'trending-down' },
      { value: 'transfer', label: 'Transfer', icon: 'swap-horiz' },
    ];

    const categoryFilters = [
      { value: 'all', label: 'All Categories' },
      ...categories.map(c => ({ value: c.value, label: c.label })),
    ];

    return (
      <View style={styles.filtersContainer}>
        <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.filterScroll}>
          {typeFilters.map((filter) => (
            <TouchableOpacity
              key={filter.value}
              style={[
                styles.filterButton,
                selectedFilter === filter.value && styles.filterButtonActive
              ]}
              onPress={() => setSelectedFilter(filter.value as TransactionType | 'all')}
            >
              <MaterialIcons
                name={filter.icon as any}
                size={16}
                color={selectedFilter === filter.value ? '#007AFF' : '#666'}
              />
              <Text style={[
                styles.filterButtonText,
                selectedFilter === filter.value && styles.filterButtonTextActive
              ]}>
                {filter.label}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>

        <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.filterScroll}>
          {categoryFilters.map((filter) => (
            <TouchableOpacity
              key={filter.value}
              style={[
                styles.categoryFilterButton,
                selectedCategory === filter.value && styles.categoryFilterButtonActive
              ]}
              onPress={() => setSelectedCategory(filter.value)}
            >
              <Text style={[
                styles.categoryFilterButtonText,
                selectedCategory === filter.value && styles.categoryFilterButtonTextActive
              ]}>
                {filter.label}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>
    );
  };

  const renderHeader = () => {
    if (!showHeader) return null;

    return (
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Transactions</Text>
        <Text style={styles.headerSubtitle}>
          {filteredTransactions.length} of {transactions.length} transaction{transactions.length !== 1 ? 's' : ''}
        </Text>
      </View>
    );
  };

  const renderEmpty = () => (
    <View style={styles.emptyContainer}>
      <MaterialIcons name="receipt" size={48} color="#ccc" />
      <Text style={styles.emptyText}>No transactions found</Text>
      <Text style={styles.emptySubtext}>Add a transaction to get started</Text>
    </View>
  );

  return (
    <View style={styles.container}>
      {renderFilters()}
      <FlatList
        data={filteredTransactions}
        renderItem={renderItem}
        keyExtractor={(item) => item.id}
        ListHeaderComponent={renderHeader}
        ListEmptyComponent={renderEmpty}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={filteredTransactions.length === 0 ? styles.emptyList : undefined}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  filtersContainer: {
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
    marginBottom: 8,
  },
  filterScroll: {
    marginBottom: 8,
  },
  filterButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    marginRight: 8,
    borderRadius: 16,
    backgroundColor: '#f5f5f5',
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  filterButtonActive: {
    backgroundColor: '#E3F2FD',
    borderColor: '#007AFF',
  },
  filterButtonText: {
    fontSize: 12,
    fontWeight: '500',
    color: '#666',
    marginLeft: 4,
  },
  filterButtonTextActive: {
    color: '#007AFF',
  },
  categoryFilterButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    marginRight: 8,
    borderRadius: 16,
    backgroundColor: '#f5f5f5',
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  categoryFilterButtonActive: {
    backgroundColor: '#E3F2FD',
    borderColor: '#007AFF',
  },
  categoryFilterButtonText: {
    fontSize: 12,
    fontWeight: '500',
    color: '#666',
  },
  categoryFilterButtonTextActive: {
    color: '#007AFF',
  },
  header: {
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
    marginBottom: 16,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#333',
  },
  headerSubtitle: {
    fontSize: 14,
    color: '#666',
    marginTop: 4,
  },
  transactionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 4,
    borderBottomWidth: 1,
    borderBottomColor: '#f5f5f5',
  },
  transactionLeft: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  transactionInfo: {
    flex: 1,
  },
  transactionDescription: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
    marginBottom: 4,
  },
  transactionMeta: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  transactionDate: {
    fontSize: 12,
    color: '#666',
  },
  metaSeparator: {
    fontSize: 12,
    color: '#666',
    marginHorizontal: 6,
  },
  transactionCategory: {
    fontSize: 12,
    color: '#666',
  },
  transactionRight: {
    alignItems: 'flex-end',
  },
  transactionAmount: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  walletName: {
    fontSize: 12,
    color: '#666',
    maxWidth: 80,
  },
  emptyContainer: {
    alignItems: 'center',
    paddingVertical: 40,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: '500',
    color: '#666',
    marginTop: 16,
  },
  emptySubtext: {
    fontSize: 14,
    color: '#999',
    marginTop: 4,
  },
  emptyList: {
    flexGrow: 1,
    justifyContent: 'center',
  },
});
