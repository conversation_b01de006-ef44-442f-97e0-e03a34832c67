import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useForm, Controller, useFieldArray } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';

import { useTransactions } from '../hooks/useTransactions';
import { formatCurrency } from '../lib/utils';
import Input from './ui/Input';
import Button from './ui/Button';
import Card from './ui/Card';
import type { Budget } from '../lib/types';

const budgetFormSchema = z.object({
  budgets: z.array(
    z.object({
      category: z.string(),
      amount: z.coerce.number().min(0, 'Budget must be a positive number'),
    })
  ),
});

type BudgetFormValues = z.infer<typeof budgetFormSchema>;

interface BudgetFormProps {
  currentBudgets: Budget[];
  onSave: (budgets: Budget[]) => void;
  onCancel: () => void;
}

export default function BudgetForm({ currentBudgets, onSave, onCancel }: BudgetFormProps) {
  const { categories } = useTransactions();
  const [loading, setLoading] = useState(false);

  // Filter categories to only show expense categories
  const expenseCategories = categories.filter(c => c.type === 'expense' || c.type === 'all');

  const {
    control,
    handleSubmit,
    formState: { errors },
  } = useForm<BudgetFormValues>({
    resolver: zodResolver(budgetFormSchema),
    defaultValues: {
      budgets: expenseCategories.map(category => {
        const existingBudget = currentBudgets.find(b => b.category === category.value);
        return {
          category: category.value,
          amount: existingBudget?.amount || 0,
        };
      }),
    },
  });

  const { fields, append, remove } = useFieldArray({
    control,
    name: 'budgets',
  });

  const onSubmit = async (data: BudgetFormValues) => {
    setLoading(true);
    try {
      const newBudgets: Budget[] = data.budgets
        .filter(b => b.amount > 0)
        .map(b => ({
          id: `budget-${b.category}`,
          category: b.category,
          amount: b.amount,
        }));
      
      await onSave(newBudgets);
    } catch (error) {
      Alert.alert('Error', 'Failed to save budgets');
    } finally {
      setLoading(false);
    }
  };

  const addBudgetForCategory = (categoryValue: string) => {
    const existingIndex = fields.findIndex(f => f.category === categoryValue);
    if (existingIndex === -1) {
      append({ category: categoryValue, amount: 0 });
    }
  };

  const removeBudgetForCategory = (index: number) => {
    remove(index);
  };

  const availableCategories = expenseCategories.filter(
    category => !fields.some(field => field.category === category.value)
  );

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      {/* Instructions */}
      <Card style={styles.instructionsCard}>
        <View style={styles.instructionsHeader}>
          <MaterialIcons name="info-outline" size={24} color="#007AFF" />
          <Text style={styles.instructionsTitle}>Set Your Budgets</Text>
        </View>
        <Text style={styles.instructionsText}>
          Set monthly spending limits for your expense categories. Leave amount as 0 to remove a budget.
        </Text>
      </Card>

      {/* Budget Fields */}
      <Card style={styles.budgetsCard}>
        <Text style={styles.sectionTitle}>Category Budgets</Text>
        
        {fields.map((field, index) => {
          const category = expenseCategories.find(c => c.value === field.category);
          const IconComponent = category?.icon || MaterialIcons;
          
          return (
            <View key={field.id} style={styles.budgetItem}>
              <View style={styles.budgetHeader}>
                <View style={styles.categoryInfo}>
                  <View style={styles.categoryIconContainer}>
                    <IconComponent 
                      name="category" 
                      size={16} 
                      color="#007AFF" 
                    />
                  </View>
                  <Text style={styles.categoryName}>
                    {category?.label || field.category}
                  </Text>
                </View>
                <TouchableOpacity
                  onPress={() => removeBudgetForCategory(index)}
                  style={styles.removeButton}
                >
                  <MaterialIcons name="close" size={20} color="#FF3B30" />
                </TouchableOpacity>
              </View>
              
              <Controller
                control={control}
                name={`budgets.${index}.amount`}
                render={({ field: { onChange, value } }) => (
                  <Input
                    placeholder="0.00"
                    keyboardType="numeric"
                    value={value?.toString() || ''}
                    onChangeText={(text) => onChange(parseFloat(text) || 0)}
                    error={errors.budgets?.[index]?.amount?.message}
                    icon="attach-money"
                    containerStyle={styles.amountInput}
                  />
                )}
              />
            </View>
          );
        })}

        {/* Add Category Button */}
        {availableCategories.length > 0 && (
          <View style={styles.addCategorySection}>
            <Text style={styles.addCategoryTitle}>Add Budget for Category:</Text>
            <ScrollView horizontal showsHorizontalScrollIndicator={false}>
              <View style={styles.categoryButtons}>
                {availableCategories.map((category) => {
                  const IconComponent = category.icon;
                  return (
                    <TouchableOpacity
                      key={category.value}
                      style={styles.categoryButton}
                      onPress={() => addBudgetForCategory(category.value)}
                    >
                      <View style={styles.categoryButtonIcon}>
                        <IconComponent 
                          name="category" 
                          size={16} 
                          color="#007AFF" 
                        />
                      </View>
                      <Text style={styles.categoryButtonText} numberOfLines={2}>
                        {category.label}
                      </Text>
                    </TouchableOpacity>
                  );
                })}
              </View>
            </ScrollView>
          </View>
        )}
      </Card>

      {/* Summary */}
      <Card style={styles.summaryCard}>
        <Text style={styles.sectionTitle}>Budget Summary</Text>
        <View style={styles.summaryRow}>
          <Text style={styles.summaryLabel}>Total Monthly Budget:</Text>
          <Text style={styles.summaryValue}>
            {formatCurrency(
              fields.reduce((sum, field, index) => {
                const amount = parseFloat(control._formValues.budgets?.[index]?.amount?.toString() || '0') || 0;
                return sum + amount;
              }, 0)
            )}
          </Text>
        </View>
        <View style={styles.summaryRow}>
          <Text style={styles.summaryLabel}>Categories with Budgets:</Text>
          <Text style={styles.summaryValue}>
            {fields.filter((_, index) => {
              const amount = parseFloat(control._formValues.budgets?.[index]?.amount?.toString() || '0') || 0;
              return amount > 0;
            }).length}
          </Text>
        </View>
      </Card>

      {/* Action Buttons */}
      <View style={styles.buttonContainer}>
        <Button
          title="Cancel"
          variant="outline"
          onPress={onCancel}
          style={styles.button}
        />
        <Button
          title="Save Budgets"
          onPress={handleSubmit(onSubmit)}
          loading={loading}
          style={styles.button}
        />
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  instructionsCard: {
    marginBottom: 16,
    backgroundColor: '#E3F2FD',
    borderColor: '#007AFF',
    borderWidth: 1,
  },
  instructionsHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  instructionsTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginLeft: 8,
  },
  instructionsText: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
  },
  budgetsCard: {
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginBottom: 16,
  },
  budgetItem: {
    marginBottom: 16,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  budgetHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  categoryInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  categoryIconContainer: {
    width: 28,
    height: 28,
    borderRadius: 14,
    backgroundColor: '#E3F2FD',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  categoryName: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
  },
  removeButton: {
    padding: 4,
  },
  amountInput: {
    marginBottom: 0,
  },
  addCategorySection: {
    marginTop: 16,
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
  },
  addCategoryTitle: {
    fontSize: 14,
    fontWeight: '500',
    color: '#666',
    marginBottom: 12,
  },
  categoryButtons: {
    flexDirection: 'row',
    paddingHorizontal: 4,
  },
  categoryButton: {
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    marginRight: 8,
    borderRadius: 8,
    backgroundColor: '#f5f5f5',
    borderWidth: 1,
    borderColor: '#e0e0e0',
    minWidth: 80,
  },
  categoryButtonIcon: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: '#E3F2FD',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 4,
  },
  categoryButtonText: {
    fontSize: 12,
    color: '#333',
    textAlign: 'center',
  },
  summaryCard: {
    marginBottom: 16,
    backgroundColor: '#F8F9FA',
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  summaryLabel: {
    fontSize: 14,
    color: '#666',
  },
  summaryValue: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 20,
  },
  button: {
    flex: 0.48,
  },
});
