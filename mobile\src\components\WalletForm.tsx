import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';

import { useTransactions } from '../hooks/useTransactions';
import { WALLET_ICONS } from '../lib/constants';
import Input from './ui/Input';
import Button from './ui/Button';
import Card from './ui/Card';
import type { Wallet, WalletFormData } from '../lib/types';

const walletSchema = z.object({
  name: z.string().min(1, 'Wallet name is required'),
  initialBalance: z.coerce.number(),
  icon: z.string().min(1, 'Please select an icon'),
});

interface WalletFormProps {
  walletToEdit?: Wallet;
  onFinished: () => void;
}

export default function WalletForm({ walletToEdit, onFinished }: WalletFormProps) {
  const { addWallet, editWallet } = useTransactions();
  const [loading, setLoading] = useState(false);

  const {
    control,
    handleSubmit,
    setValue,
    watch,
    reset,
    formState: { errors },
  } = useForm<WalletFormData>({
    resolver: zodResolver(walletSchema),
    defaultValues: {
      name: '',
      initialBalance: 0,
      icon: WALLET_ICONS[0].name,
    },
  });

  const selectedIcon = watch('icon');

  useEffect(() => {
    if (walletToEdit) {
      reset({
        name: walletToEdit.name,
        initialBalance: walletToEdit.balance,
        icon: walletToEdit.icon,
      });
    }
  }, [walletToEdit, reset]);

  const onSubmit = async (data: WalletFormData) => {
    setLoading(true);
    try {
      if (walletToEdit) {
        await editWallet(walletToEdit.id, data);
      } else {
        await addWallet(data);
      }
      onFinished();
    } catch (error) {
      Alert.alert('Error', 'Failed to save wallet');
    } finally {
      setLoading(false);
    }
  };

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      {/* Wallet Name */}
      <Card style={styles.section}>
        <Controller
          control={control}
          name="name"
          render={({ field: { onChange, value } }) => (
            <Input
              label="Wallet Name"
              placeholder="e.g., Cash, Bank Account, Credit Card"
              value={value}
              onChangeText={onChange}
              error={errors.name?.message}
              icon="account-balance-wallet"
            />
          )}
        />
      </Card>

      {/* Initial Balance */}
      <Card style={styles.section}>
        <Controller
          control={control}
          name="initialBalance"
          render={({ field: { onChange, value } }) => (
            <Input
              label={walletToEdit ? "Current Balance" : "Initial Balance"}
              placeholder="0.00"
              keyboardType="numeric"
              value={value?.toString() || ''}
              onChangeText={(text) => onChange(parseFloat(text) || 0)}
              error={errors.initialBalance?.message}
              icon="attach-money"
            />
          )}
        />
      </Card>

      {/* Icon Selection */}
      <Card style={styles.section}>
        <Text style={styles.sectionTitle}>Choose an Icon</Text>
        <Controller
          control={control}
          name="icon"
          render={({ field: { onChange, value } }) => (
            <View style={styles.iconGrid}>
              {WALLET_ICONS.map((iconInfo) => {
                const IconComponent = iconInfo.icon;
                const isSelected = value === iconInfo.name;
                
                return (
                  <TouchableOpacity
                    key={iconInfo.name}
                    style={[
                      styles.iconButton,
                      isSelected && styles.iconButtonSelected
                    ]}
                    onPress={() => onChange(iconInfo.name)}
                  >
                    <IconComponent 
                      name={iconInfo.name as any} 
                      size={24} 
                      color={isSelected ? '#007AFF' : '#666'} 
                    />
                  </TouchableOpacity>
                );
              })}
            </View>
          )}
        />
        {errors.icon && (
          <Text style={styles.errorText}>{errors.icon.message}</Text>
        )}
      </Card>

      {/* Preview */}
      <Card style={styles.section}>
        <Text style={styles.sectionTitle}>Preview</Text>
        <View style={styles.preview}>
          <View style={styles.previewIconContainer}>
            {(() => {
              const iconInfo = WALLET_ICONS.find(i => i.name === selectedIcon);
              const IconComponent = iconInfo?.icon || MaterialIcons;
              return (
                <IconComponent 
                  name={selectedIcon as any} 
                  size={32} 
                  color="#007AFF" 
                />
              );
            })()}
          </View>
          <View style={styles.previewInfo}>
            <Text style={styles.previewName}>
              {watch('name') || 'Wallet Name'}
            </Text>
            <Text style={styles.previewBalance}>
              ${(watch('initialBalance') || 0).toFixed(2)}
            </Text>
          </View>
        </View>
      </Card>

      {/* Submit Button */}
      <View style={styles.buttonContainer}>
        <Button
          title={walletToEdit ? 'Update Wallet' : 'Create Wallet'}
          onPress={handleSubmit(onSubmit)}
          loading={loading}
          size="large"
        />
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  section: {
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 12,
  },
  iconGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginHorizontal: -4,
  },
  iconButton: {
    width: 56,
    height: 56,
    margin: 4,
    borderRadius: 28,
    backgroundColor: '#f5f5f5',
    borderWidth: 2,
    borderColor: '#e0e0e0',
    justifyContent: 'center',
    alignItems: 'center',
  },
  iconButtonSelected: {
    backgroundColor: '#E3F2FD',
    borderColor: '#007AFF',
  },
  errorText: {
    fontSize: 14,
    color: '#FF3B30',
    marginTop: 8,
  },
  preview: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#f9f9f9',
    borderRadius: 8,
  },
  previewIconContainer: {
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: '#E3F2FD',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  previewInfo: {
    flex: 1,
  },
  previewName: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginBottom: 4,
  },
  previewBalance: {
    fontSize: 16,
    color: '#007AFF',
    fontWeight: '500',
  },
  buttonContainer: {
    paddingVertical: 20,
  },
});
