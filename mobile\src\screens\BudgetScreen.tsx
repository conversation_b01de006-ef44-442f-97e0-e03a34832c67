import React, { useState, useMemo } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { isSameMonth } from 'date-fns';
import { useTransactions } from '../hooks/useTransactions';
import { formatCurrency, calculatePercentage } from '../lib/utils';
import Card from '../components/ui/Card';
import Button from '../components/ui/Button';
import Modal from '../components/ui/Modal';
import BudgetForm from '../components/BudgetForm';
import type { Budget } from '../lib/types';

export default function BudgetScreen() {
  const { budgets, transactions, categories, isInitialized, setBudgets } = useTransactions();
  const [isBudgetFormOpen, setIsBudgetFormOpen] = useState(false);

  // Calculate current month spending by category
  const currentMonthSpending = useMemo(() => {
    const now = new Date();
    const currentMonthTransactions = transactions.filter(t =>
      t.type === 'expense' && isSameMonth(t.date, now)
    );

    const spendingByCategory = new Map<string, number>();
    currentMonthTransactions.forEach(transaction => {
      const current = spendingByCategory.get(transaction.category) || 0;
      spendingByCategory.set(transaction.category, current + transaction.amount);
    });

    return spendingByCategory;
  }, [transactions]);

  // Calculate budget progress
  const budgetProgress = useMemo(() => {
    return budgets.map(budget => {
      const spent = currentMonthSpending.get(budget.category) || 0;
      const percentage = calculatePercentage(spent, budget.amount);
      const category = categories.find(c => c.value === budget.category);

      return {
        ...budget,
        spent,
        percentage: Math.min(percentage, 100),
        remaining: Math.max(budget.amount - spent, 0),
        isOverBudget: spent > budget.amount,
        categoryLabel: category?.label || budget.category,
      };
    });
  }, [budgets, currentMonthSpending, categories]);

  const totalBudget = budgets.reduce((sum, budget) => sum + budget.amount, 0);
  const totalSpent = budgetProgress.reduce((sum, budget) => sum + budget.spent, 0);
  const totalRemaining = Math.max(totalBudget - totalSpent, 0);

  const handleSaveBudgets = async (newBudgets: Budget[]) => {
    await setBudgets(newBudgets);
    setIsBudgetFormOpen(false);
  };

  if (!isInitialized) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#007AFF" />
        <Text style={styles.loadingText}>Loading budgets...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Overview Card */}
        <Card style={styles.overviewCard}>
          <Text style={styles.overviewTitle}>Budget Overview</Text>
          <View style={styles.overviewStats}>
            <View style={styles.overviewStat}>
              <Text style={styles.overviewStatLabel}>Total Budget</Text>
              <Text style={styles.overviewStatValue}>
                {formatCurrency(totalBudget)}
              </Text>
            </View>
            <View style={styles.overviewStat}>
              <Text style={styles.overviewStatLabel}>Spent</Text>
              <Text style={[styles.overviewStatValue, styles.spentText]}>
                {formatCurrency(totalSpent)}
              </Text>
            </View>
            <View style={styles.overviewStat}>
              <Text style={styles.overviewStatLabel}>Remaining</Text>
              <Text style={[styles.overviewStatValue, styles.remainingText]}>
                {formatCurrency(totalRemaining)}
              </Text>
            </View>
          </View>

          {totalBudget > 0 && (
            <View style={styles.overviewProgress}>
              <View style={styles.progressBar}>
                <View
                  style={[
                    styles.progressFill,
                    {
                      width: `${Math.min(calculatePercentage(totalSpent, totalBudget), 100)}%`,
                      backgroundColor: totalSpent > totalBudget ? '#FF3B30' : '#007AFF'
                    }
                  ]}
                />
              </View>
              <Text style={styles.progressText}>
                {calculatePercentage(totalSpent, totalBudget).toFixed(1)}% used
              </Text>
            </View>
          )}
        </Card>

        {/* Budget List */}
        <View style={styles.budgetSection}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Category Budgets</Text>
            <Button
              title={budgets.length === 0 ? "Set Budgets" : "Edit Budgets"}
              onPress={() => setIsBudgetFormOpen(true)}
              size="small"
              style={styles.editButton}
            />
          </View>

          {budgets.length === 0 ? (
            <Card style={styles.emptyCard}>
              <View style={styles.emptyState}>
                <MaterialIcons name="target" size={48} color="#ccc" />
                <Text style={styles.emptyTitle}>No Budgets Set</Text>
                <Text style={styles.emptySubtitle}>
                  Set budgets for your expense categories to track your spending
                </Text>
                <Button
                  title="Set Your First Budget"
                  onPress={() => setIsBudgetFormOpen(true)}
                  style={styles.emptyButton}
                />
              </View>
            </Card>
          ) : (
            budgetProgress.map((budget) => (
              <BudgetCard key={budget.id} budget={budget} />
            ))
          )}
        </View>

        {/* Tips Card */}
        <Card style={styles.tipsCard}>
          <View style={styles.tipsHeader}>
            <MaterialIcons name="lightbulb-outline" size={24} color="#FFB000" />
            <Text style={styles.tipsTitle}>Budget Tips</Text>
          </View>
          <View style={styles.tipsList}>
            <Text style={styles.tipItem}>• Review and adjust budgets monthly</Text>
            <Text style={styles.tipItem}>• Set realistic spending limits</Text>
            <Text style={styles.tipItem}>• Track progress regularly</Text>
            <Text style={styles.tipItem}>• Consider seasonal variations</Text>
          </View>
        </Card>
      </ScrollView>

      {/* Budget Form Modal */}
      <Modal
        visible={isBudgetFormOpen}
        onClose={() => setIsBudgetFormOpen(false)}
        title="Manage Budgets"
        fullScreen
      >
        <BudgetForm
          currentBudgets={budgets}
          onSave={handleSaveBudgets}
          onCancel={() => setIsBudgetFormOpen(false)}
        />
      </Modal>
    </View>
  );
}

interface BudgetCardProps {
  budget: {
    id: string;
    category: string;
    amount: number;
    spent: number;
    percentage: number;
    remaining: number;
    isOverBudget: boolean;
    categoryLabel: string;
  };
}

function BudgetCard({ budget }: BudgetCardProps) {
  const getStatusColor = () => {
    if (budget.isOverBudget) return '#FF3B30';
    if (budget.percentage > 80) return '#FF9500';
    return '#4CAF50';
  };

  const getStatusText = () => {
    if (budget.isOverBudget) return 'Over Budget';
    if (budget.percentage > 80) return 'Almost There';
    return 'On Track';
  };

  return (
    <Card style={styles.budgetCard}>
      <View style={styles.budgetHeader}>
        <View style={styles.budgetInfo}>
          <Text style={styles.budgetCategory}>{budget.categoryLabel}</Text>
          <Text style={[styles.budgetStatus, { color: getStatusColor() }]}>
            {getStatusText()}
          </Text>
        </View>
        <View style={styles.budgetAmounts}>
          <Text style={styles.budgetSpent}>
            {formatCurrency(budget.spent)}
          </Text>
          <Text style={styles.budgetTotal}>
            of {formatCurrency(budget.amount)}
          </Text>
        </View>
      </View>

      <View style={styles.budgetProgress}>
        <View style={styles.progressBar}>
          <View
            style={[
              styles.progressFill,
              {
                width: `${budget.percentage}%`,
                backgroundColor: getStatusColor()
              }
            ]}
          />
        </View>
        <Text style={styles.progressPercentage}>
          {budget.percentage.toFixed(1)}%
        </Text>
      </View>

      <View style={styles.budgetFooter}>
        <Text style={styles.budgetRemaining}>
          {budget.isOverBudget
            ? `${formatCurrency(budget.spent - budget.amount)} over budget`
            : `${formatCurrency(budget.remaining)} remaining`
          }
        </Text>
      </View>
    </Card>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    color: '#666',
  },
  scrollView: {
    flex: 1,
    padding: 16,
  },
  overviewCard: {
    marginBottom: 24,
  },
  overviewTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#333',
    marginBottom: 16,
    textAlign: 'center',
  },
  overviewStats: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  overviewStat: {
    flex: 1,
    alignItems: 'center',
  },
  overviewStatLabel: {
    fontSize: 12,
    color: '#666',
    marginBottom: 4,
  },
  overviewStatValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  spentText: {
    color: '#FF3B30',
  },
  remainingText: {
    color: '#4CAF50',
  },
  overviewProgress: {
    alignItems: 'center',
  },
  progressBar: {
    width: '100%',
    height: 8,
    backgroundColor: '#f0f0f0',
    borderRadius: 4,
    marginBottom: 8,
  },
  progressFill: {
    height: '100%',
    borderRadius: 4,
  },
  progressText: {
    fontSize: 14,
    color: '#666',
  },
  budgetSection: {
    marginBottom: 24,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#333',
  },
  editButton: {
    paddingHorizontal: 16,
  },
  emptyCard: {
    paddingVertical: 40,
  },
  emptyState: {
    alignItems: 'center',
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginTop: 16,
  },
  emptySubtitle: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    marginTop: 8,
    marginBottom: 24,
  },
  emptyButton: {
    paddingHorizontal: 24,
  },
  budgetCard: {
    marginBottom: 12,
  },
  budgetHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  budgetInfo: {
    flex: 1,
  },
  budgetCategory: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 2,
  },
  budgetStatus: {
    fontSize: 12,
    fontWeight: '500',
  },
  budgetAmounts: {
    alignItems: 'flex-end',
  },
  budgetSpent: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  budgetTotal: {
    fontSize: 12,
    color: '#666',
  },
  budgetProgress: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  progressPercentage: {
    fontSize: 12,
    color: '#666',
    marginLeft: 8,
    minWidth: 40,
  },
  budgetFooter: {
    alignItems: 'center',
  },
  budgetRemaining: {
    fontSize: 14,
    color: '#666',
  },
  tipsCard: {
    backgroundColor: '#FFF9E6',
    borderColor: '#FFB000',
    borderWidth: 1,
  },
  tipsHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  tipsTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginLeft: 8,
  },
  tipsList: {
    paddingLeft: 8,
  },
  tipItem: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
    lineHeight: 20,
  },
});

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginTop: 16,
  },
  subtitle: {
    fontSize: 18,
    color: '#666',
    marginTop: 8,
  },
  description: {
    fontSize: 16,
    color: '#999',
    textAlign: 'center',
    marginTop: 16,
    lineHeight: 24,
  },
});
