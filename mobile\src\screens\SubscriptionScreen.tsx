import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { WebView } from 'react-native-webview';

import { useTransactions } from '../hooks/useTransactions';
import { FREE_TIER_LIMITS } from '../lib/constants';
import Card from '../components/ui/Card';
import Button from '../components/ui/Button';
import Modal from '../components/ui/Modal';

export default function SubscriptionScreen() {
  const { subscription, wallets, categories, upgradeToPremium } = useTransactions();
  const [isPayPalModalOpen, setIsPayPalModalOpen] = useState(false);
  const [isProcessingPayment, setIsProcessingPayment] = useState(false);

  const isPremium = subscription.tier === 'premium';

  const freeFeatures = [
    { text: `Up to ${FREE_TIER_LIMITS.wallets} wallets`, icon: 'account-balance-wallet' },
    { text: `Up to ${FREE_TIER_LIMITS.categories} custom categories`, icon: 'category' },
    { text: `${FREE_TIER_LIMITS.historyMonths} months transaction history`, icon: 'history' },
    { text: 'Basic analytics', icon: 'bar-chart' },
    { text: 'Budget tracking', icon: 'target' },
  ];

  const premiumFeatures = [
    { text: 'Unlimited wallets', icon: 'account-balance-wallet' },
    { text: 'Unlimited custom categories', icon: 'category' },
    { text: 'Unlimited transaction history', icon: 'history' },
    { text: 'Advanced analytics & insights', icon: 'analytics' },
    { text: 'Export data to CSV/PDF', icon: 'file-download' },
    { text: 'Priority customer support', icon: 'support' },
    { text: 'Dark mode theme', icon: 'dark-mode' },
    { text: 'Recurring transaction templates', icon: 'repeat' },
  ];

  const handleUpgrade = () => {
    if (isPremium) {
      Alert.alert('Already Premium', 'You already have premium access!');
      return;
    }
    setIsPayPalModalOpen(true);
  };

  const handlePayPalSuccess = async () => {
    setIsProcessingPayment(true);
    try {
      // Simulate payment processing
      await new Promise(resolve => setTimeout(resolve, 2000));
      await upgradeToPremium();
      setIsPayPalModalOpen(false);
      Alert.alert(
        'Welcome to Premium!',
        'Your account has been upgraded successfully. Enjoy unlimited features!'
      );
    } catch (error) {
      Alert.alert('Payment Error', 'Failed to process payment. Please try again.');
    } finally {
      setIsProcessingPayment(false);
    }
  };

  const renderPayPalWebView = () => {
    // In a real app, this would be the actual PayPal checkout URL
    const paypalUrl = `https://www.paypal.com/checkoutnow?token=demo_token`;

    return (
      <WebView
        source={{ uri: paypalUrl }}
        onNavigationStateChange={(navState) => {
          // Handle PayPal success/cancel URLs
          if (navState.url.includes('success')) {
            handlePayPalSuccess();
          } else if (navState.url.includes('cancel')) {
            setIsPayPalModalOpen(false);
          }
        }}
        style={styles.webview}
      />
    );
  };

  return (
    <View style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Current Plan */}
        <Card style={[styles.planCard, isPremium && styles.premiumCard]}>
          <View style={styles.planHeader}>
            <MaterialIcons
              name={isPremium ? 'star' : 'star-border'}
              size={32}
              color={isPremium ? '#FFD700' : '#666'}
            />
            <Text style={styles.planTitle}>
              {isPremium ? 'Premium Plan' : 'Free Plan'}
            </Text>
            {isPremium && (
              <View style={styles.premiumBadge}>
                <Text style={styles.premiumBadgeText}>ACTIVE</Text>
              </View>
            )}
          </View>

          {!isPremium && (
            <View style={styles.usageStats}>
              <View style={styles.usageStat}>
                <Text style={styles.usageLabel}>Wallets</Text>
                <Text style={styles.usageValue}>
                  {wallets.length} / {FREE_TIER_LIMITS.wallets}
                </Text>
                <View style={styles.usageBar}>
                  <View
                    style={[
                      styles.usageBarFill,
                      {
                        width: `${Math.min((wallets.length / FREE_TIER_LIMITS.wallets) * 100, 100)}%`,
                        backgroundColor: wallets.length >= FREE_TIER_LIMITS.wallets ? '#FF3B30' : '#007AFF'
                      }
                    ]}
                  />
                </View>
              </View>

              <View style={styles.usageStat}>
                <Text style={styles.usageLabel}>Categories</Text>
                <Text style={styles.usageValue}>
                  {categories.length} / {FREE_TIER_LIMITS.categories}
                </Text>
                <View style={styles.usageBar}>
                  <View
                    style={[
                      styles.usageBarFill,
                      {
                        width: `${Math.min((categories.length / FREE_TIER_LIMITS.categories) * 100, 100)}%`,
                        backgroundColor: categories.length >= FREE_TIER_LIMITS.categories ? '#FF3B30' : '#007AFF'
                      }
                    ]}
                  />
                </View>
              </View>
            </View>
          )}
        </Card>

        {/* Feature Comparison */}
        <View style={styles.comparisonSection}>
          <Text style={styles.sectionTitle}>Feature Comparison</Text>

          {/* Free Features */}
          <Card style={styles.featureCard}>
            <View style={styles.featureHeader}>
              <MaterialIcons name="star-border" size={24} color="#666" />
              <Text style={styles.featureTitle}>Free Plan</Text>
              <Text style={styles.featurePrice}>$0</Text>
            </View>
            <View style={styles.featureList}>
              {freeFeatures.map((feature, index) => (
                <View key={index} style={styles.featureItem}>
                  <MaterialIcons name={feature.icon as any} size={16} color="#4CAF50" />
                  <Text style={styles.featureText}>{feature.text}</Text>
                </View>
              ))}
            </View>
          </Card>

          {/* Premium Features */}
          <Card style={[styles.featureCard, styles.premiumFeatureCard]}>
            <View style={styles.featureHeader}>
              <MaterialIcons name="star" size={24} color="#FFD700" />
              <Text style={styles.featureTitle}>Premium Plan</Text>
              <Text style={styles.featurePrice}>$9.99</Text>
            </View>
            <View style={styles.featureList}>
              {premiumFeatures.map((feature, index) => (
                <View key={index} style={styles.featureItem}>
                  <MaterialIcons name={feature.icon as any} size={16} color="#FFD700" />
                  <Text style={styles.featureText}>{feature.text}</Text>
                </View>
              ))}
            </View>

            {!isPremium && (
              <Button
                title="Upgrade to Premium"
                onPress={handleUpgrade}
                style={styles.upgradeButton}
              />
            )}
          </Card>
        </View>

        {/* FAQ */}
        <Card style={styles.faqCard}>
          <Text style={styles.sectionTitle}>Frequently Asked Questions</Text>
          <View style={styles.faqList}>
            <View style={styles.faqItem}>
              <Text style={styles.faqQuestion}>Is this a one-time payment?</Text>
              <Text style={styles.faqAnswer}>
                Yes! Premium is a one-time purchase with lifetime access to all premium features.
              </Text>
            </View>
            <View style={styles.faqItem}>
              <Text style={styles.faqQuestion}>Can I downgrade later?</Text>
              <Text style={styles.faqAnswer}>
                Premium is permanent, but you can contact support if you have concerns.
              </Text>
            </View>
            <View style={styles.faqItem}>
              <Text style={styles.faqQuestion}>Is my data secure?</Text>
              <Text style={styles.faqAnswer}>
                Yes, all your financial data is encrypted and stored securely in Supabase.
              </Text>
            </View>
          </View>
        </Card>
      </ScrollView>

      {/* PayPal Payment Modal */}
      <Modal
        visible={isPayPalModalOpen}
        onClose={() => setIsPayPalModalOpen(false)}
        title="Complete Payment"
        fullScreen
      >
        {isProcessingPayment ? (
          <View style={styles.processingContainer}>
            <ActivityIndicator size="large" color="#007AFF" />
            <Text style={styles.processingText}>Processing payment...</Text>
          </View>
        ) : (
          <View style={styles.paymentContainer}>
            <Text style={styles.paymentTitle}>Upgrade to Premium</Text>
            <Text style={styles.paymentSubtitle}>One-time payment of $9.99</Text>

            {/* Demo PayPal Button */}
            <TouchableOpacity
              style={styles.paypalButton}
              onPress={handlePayPalSuccess}
            >
              <Text style={styles.paypalButtonText}>Pay with PayPal (Demo)</Text>
            </TouchableOpacity>

            <Text style={styles.paymentNote}>
              This is a demo. In production, this would integrate with actual PayPal checkout.
            </Text>
          </View>
        )}
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  scrollView: {
    flex: 1,
    padding: 16,
  },
  planCard: {
    marginBottom: 24,
    borderWidth: 2,
    borderColor: '#e0e0e0',
  },
  premiumCard: {
    borderColor: '#FFD700',
    backgroundColor: '#FFFBF0',
  },
  planHeader: {
    alignItems: 'center',
    marginBottom: 16,
  },
  planTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginTop: 8,
  },
  premiumBadge: {
    backgroundColor: '#FFD700',
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 12,
    marginTop: 8,
  },
  premiumBadgeText: {
    fontSize: 12,
    fontWeight: 'bold',
    color: '#333',
  },
  usageStats: {
    marginTop: 16,
  },
  usageStat: {
    marginBottom: 16,
  },
  usageLabel: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  usageValue: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 8,
  },
  usageBar: {
    height: 6,
    backgroundColor: '#f0f0f0',
    borderRadius: 3,
  },
  usageBarFill: {
    height: '100%',
    borderRadius: 3,
  },
  comparisonSection: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#333',
    marginBottom: 16,
    textAlign: 'center',
  },
  featureCard: {
    marginBottom: 16,
  },
  premiumFeatureCard: {
    borderWidth: 2,
    borderColor: '#FFD700',
    backgroundColor: '#FFFBF0',
  },
  featureHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 16,
    paddingBottom: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  featureTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    flex: 1,
    marginLeft: 8,
  },
  featurePrice: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#007AFF',
  },
  featureList: {
    marginBottom: 16,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  featureText: {
    fontSize: 14,
    color: '#333',
    marginLeft: 8,
    flex: 1,
  },
  upgradeButton: {
    marginTop: 8,
  },
  faqCard: {
    marginBottom: 24,
  },
  faqList: {
    marginTop: 8,
  },
  faqItem: {
    marginBottom: 16,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  faqQuestion: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 4,
  },
  faqAnswer: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
  },
  webview: {
    flex: 1,
  },
  processingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  processingText: {
    fontSize: 16,
    color: '#666',
    marginTop: 16,
  },
  paymentContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  paymentTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  paymentSubtitle: {
    fontSize: 18,
    color: '#666',
    marginBottom: 40,
  },
  paypalButton: {
    backgroundColor: '#0070BA',
    paddingHorizontal: 40,
    paddingVertical: 16,
    borderRadius: 8,
    marginBottom: 20,
  },
  paypalButtonText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: '600',
  },
  paymentNote: {
    fontSize: 12,
    color: '#999',
    textAlign: 'center',
    fontStyle: 'italic',
  },
});

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginTop: 16,
  },
  subtitle: {
    fontSize: 18,
    color: '#666',
    marginTop: 8,
  },
  description: {
    fontSize: 16,
    color: '#999',
    textAlign: 'center',
    marginTop: 16,
    lineHeight: 24,
  },
});
