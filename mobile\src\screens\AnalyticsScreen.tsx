import React, { useState, useMemo } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Dimensions,
  ActivityIndicator,
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { format, subMonths, addMonths, isSameMonth, startOfMonth, endOfMonth, eachDayOfInterval } from 'date-fns';
import { VictoryChart, VictoryLine, VictoryArea, VictoryAxis, VictoryPie, VictoryBar, VictoryTheme } from 'victory-native';

import { useTransactions } from '../hooks/useTransactions';
import { formatCurrency, calculatePercentage } from '../lib/utils';
import { CHART_COLORS } from '../lib/constants';
import Card from '../components/ui/Card';
import type { Transaction } from '../lib/types';

const { width: screenWidth } = Dimensions.get('window');
const chartWidth = screenWidth - 64;

export default function AnalyticsScreen() {
  const { transactions, categories, isInitialized } = useTransactions();
  const [currentMonth, setCurrentMonth] = useState(new Date());

  const monthlyTransactions = useMemo(() => {
    return transactions.filter(t => isSameMonth(t.date, currentMonth));
  }, [transactions, currentMonth]);

  const handlePrevMonth = () => {
    setCurrentMonth(prev => subMonths(prev, 1));
  };

  const handleNextMonth = () => {
    setCurrentMonth(prev => addMonths(prev, 1));
  };

  const isNextMonthDisabled = isSameMonth(currentMonth, new Date());

  // Calculate monthly totals
  const monthlyTotals = useMemo(() => {
    const income = monthlyTransactions
      .filter(t => t.type === 'income')
      .reduce((sum, t) => sum + t.amount, 0);

    const expenses = monthlyTransactions
      .filter(t => t.type === 'expense')
      .reduce((sum, t) => sum + t.amount, 0);

    return { income, expenses, net: income - expenses };
  }, [monthlyTransactions]);

  // Category spending data for pie chart
  const categoryData = useMemo(() => {
    const expenseTransactions = monthlyTransactions.filter(t => t.type === 'expense');
    const dataMap = new Map<string, number>();

    expenseTransactions.forEach(tx => {
      const currentAmount = dataMap.get(tx.category) || 0;
      dataMap.set(tx.category, currentAmount + tx.amount);
    });

    return Array.from(dataMap.entries())
      .map(([category, amount], index) => ({
        x: categories.find(c => c.value === category)?.label || category,
        y: amount,
        fill: CHART_COLORS[index % CHART_COLORS.length],
      }))
      .sort((a, b) => b.y - a.y)
      .slice(0, 6); // Top 6 categories
  }, [monthlyTransactions, categories]);

  // Daily spending trend for the month
  const dailySpendingData = useMemo(() => {
    const monthStart = startOfMonth(currentMonth);
    const monthEnd = endOfMonth(currentMonth);
    const days = eachDayOfInterval({ start: monthStart, end: monthEnd });

    return days.map(day => {
      const dayExpenses = monthlyTransactions
        .filter(t => t.type === 'expense' && isSameMonth(t.date, day) && t.date.getDate() === day.getDate())
        .reduce((sum, t) => sum + t.amount, 0);

      return {
        x: day.getDate(),
        y: dayExpenses,
      };
    });
  }, [monthlyTransactions, currentMonth]);

  if (!isInitialized) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#007AFF" />
        <Text style={styles.loadingText}>Loading analytics...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Month Navigation */}
        <View style={styles.monthNavigation}>
          <TouchableOpacity onPress={handlePrevMonth} style={styles.navButton}>
            <MaterialIcons name="chevron-left" size={24} color="#007AFF" />
          </TouchableOpacity>
          <Text style={styles.monthTitle}>
            {format(currentMonth, 'MMMM yyyy')}
          </Text>
          <TouchableOpacity
            onPress={handleNextMonth}
            style={[styles.navButton, isNextMonthDisabled && styles.navButtonDisabled]}
            disabled={isNextMonthDisabled}
          >
            <MaterialIcons
              name="chevron-right"
              size={24}
              color={isNextMonthDisabled ? '#ccc' : '#007AFF'}
            />
          </TouchableOpacity>
        </View>

        {/* Monthly Summary */}
        <Card style={styles.summaryCard}>
          <Text style={styles.cardTitle}>Monthly Summary</Text>
          <View style={styles.summaryGrid}>
            <View style={styles.summaryItem}>
              <MaterialIcons name="trending-up" size={20} color="#4CAF50" />
              <Text style={styles.summaryLabel}>Income</Text>
              <Text style={[styles.summaryValue, styles.incomeText]}>
                {formatCurrency(monthlyTotals.income)}
              </Text>
            </View>
            <View style={styles.summaryItem}>
              <MaterialIcons name="trending-down" size={20} color="#F44336" />
              <Text style={styles.summaryLabel}>Expenses</Text>
              <Text style={[styles.summaryValue, styles.expenseText]}>
                {formatCurrency(monthlyTotals.expenses)}
              </Text>
            </View>
            <View style={styles.summaryItem}>
              <MaterialIcons
                name="account-balance"
                size={20}
                color={monthlyTotals.net >= 0 ? '#4CAF50' : '#F44336'}
              />
              <Text style={styles.summaryLabel}>Net</Text>
              <Text style={[
                styles.summaryValue,
                { color: monthlyTotals.net >= 0 ? '#4CAF50' : '#F44336' }
              ]}>
                {formatCurrency(monthlyTotals.net)}
              </Text>
            </View>
          </View>
        </Card>

        {/* Daily Spending Trend */}
        {dailySpendingData.length > 0 && (
          <Card style={styles.chartCard}>
            <Text style={styles.cardTitle}>Daily Spending Trend</Text>
            <VictoryChart
              theme={VictoryTheme.material}
              width={chartWidth}
              height={200}
              padding={{ left: 60, top: 20, right: 40, bottom: 40 }}
            >
              <VictoryAxis dependentAxis tickFormat={(x) => `$${x}`} />
              <VictoryAxis />
              <VictoryArea
                data={dailySpendingData}
                style={{
                  data: { fill: '#007AFF', fillOpacity: 0.3, stroke: '#007AFF', strokeWidth: 2 }
                }}
                animate={{
                  duration: 1000,
                  onLoad: { duration: 500 }
                }}
              />
            </VictoryChart>
          </Card>
        )}

        {/* Category Breakdown */}
        {categoryData.length > 0 && (
          <Card style={styles.chartCard}>
            <Text style={styles.cardTitle}>Spending by Category</Text>
            <View style={styles.pieChartContainer}>
              <VictoryPie
                data={categoryData}
                width={chartWidth}
                height={250}
                innerRadius={50}
                colorScale={CHART_COLORS}
                labelComponent={<></>}
                animate={{
                  duration: 1000,
                }}
              />
            </View>
            <View style={styles.legendContainer}>
              {categoryData.map((item, index) => (
                <View key={index} style={styles.legendItem}>
                  <View style={[styles.legendColor, { backgroundColor: item.fill }]} />
                  <Text style={styles.legendLabel} numberOfLines={1}>
                    {item.x}
                  </Text>
                  <Text style={styles.legendValue}>
                    {formatCurrency(item.y)}
                  </Text>
                </View>
              ))}
            </View>
          </Card>
        )}

        {/* Empty State */}
        {monthlyTransactions.length === 0 && (
          <Card style={styles.emptyCard}>
            <View style={styles.emptyState}>
              <MaterialIcons name="bar-chart" size={48} color="#ccc" />
              <Text style={styles.emptyTitle}>No Data for This Month</Text>
              <Text style={styles.emptySubtitle}>
                Add some transactions to see your analytics
              </Text>
            </View>
          </Card>
        )}
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    color: '#666',
  },
  scrollView: {
    flex: 1,
    padding: 16,
  },
  monthNavigation: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
    paddingHorizontal: 8,
  },
  navButton: {
    padding: 8,
    borderRadius: 20,
    backgroundColor: '#fff',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  navButtonDisabled: {
    opacity: 0.5,
  },
  monthTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#333',
  },
  summaryCard: {
    marginBottom: 16,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginBottom: 16,
    textAlign: 'center',
  },
  summaryGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  summaryItem: {
    flex: 1,
    alignItems: 'center',
  },
  summaryLabel: {
    fontSize: 12,
    color: '#666',
    marginTop: 4,
    marginBottom: 4,
  },
  summaryValue: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  incomeText: {
    color: '#4CAF50',
  },
  expenseText: {
    color: '#F44336',
  },
  chartCard: {
    marginBottom: 16,
    alignItems: 'center',
  },
  pieChartContainer: {
    alignItems: 'center',
  },
  legendContainer: {
    width: '100%',
    marginTop: 16,
  },
  legendItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  legendColor: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 8,
  },
  legendLabel: {
    flex: 1,
    fontSize: 14,
    color: '#333',
  },
  legendValue: {
    fontSize: 14,
    fontWeight: '500',
    color: '#666',
  },
  emptyCard: {
    paddingVertical: 40,
  },
  emptyState: {
    alignItems: 'center',
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginTop: 16,
  },
  emptySubtitle: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    marginTop: 8,
  },
});

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginTop: 16,
  },
  subtitle: {
    fontSize: 18,
    color: '#666',
    marginTop: 8,
  },
  description: {
    fontSize: 16,
    color: '#999',
    textAlign: 'center',
    marginTop: 16,
    lineHeight: 24,
  },
});
