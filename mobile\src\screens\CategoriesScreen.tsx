import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useTransactions } from '../hooks/useTransactions';
import { CATEGORY_ICONS } from '../lib/constants';
import Card from '../components/ui/Card';
import Button from '../components/ui/Button';
import Modal from '../components/ui/Modal';
import CategoryForm from '../components/CategoryForm';
import type { Category, CategoryInfo, TransactionType } from '../lib/types';

export default function CategoriesScreen() {
  const { categories, isInitialized, deleteCategory, subscription } = useTransactions();
  const [selectedCategory, setSelectedCategory] = useState<Category | undefined>();
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedType, setSelectedType] = useState<TransactionType | 'all'>('all');

  const filteredCategories = categories.filter(
    c => selectedType === 'all' || c.type === selectedType || c.type === 'all'
  );

  const handleAddCategory = () => {
    setSelectedCategory(undefined);
    setIsFormOpen(true);
  };

  const handleEditCategory = (category: Category) => {
    setSelectedCategory(category);
    setIsFormOpen(true);
  };

  const handleDeleteCategory = (category: Category) => {
    setSelectedCategory(category);
    setIsDeleteDialogOpen(true);
  };

  const confirmDelete = async () => {
    if (selectedCategory) {
      await deleteCategory(selectedCategory.value);
      setIsDeleteDialogOpen(false);
      setSelectedCategory(undefined);
    }
  };

  const handleCloseForm = () => {
    setIsFormOpen(false);
    setSelectedCategory(undefined);
  };

  if (!isInitialized) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#007AFF" />
        <Text style={styles.loadingText}>Loading categories...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View style={styles.header}>
          <View style={styles.headerLeft}>
            <Text style={styles.headerTitle}>Categories</Text>
            <Text style={styles.headerSubtitle}>
              {categories.length} categor{categories.length !== 1 ? 'ies' : 'y'}
            </Text>
          </View>
          <Button
            title="Add Category"
            onPress={handleAddCategory}
            size="small"
            style={styles.addButton}
          />
        </View>

        {/* Type Filter */}
        <Card style={styles.filterCard}>
          <Text style={styles.filterTitle}>Filter by Type</Text>
          <View style={styles.filterButtons}>
            {[
              { value: 'all', label: 'All', icon: 'list' },
              { value: 'income', label: 'Income', icon: 'trending-up' },
              { value: 'expense', label: 'Expense', icon: 'trending-down' },
            ].map((filter) => (
              <TouchableOpacity
                key={filter.value}
                style={[
                  styles.filterButton,
                  selectedType === filter.value && styles.filterButtonActive
                ]}
                onPress={() => setSelectedType(filter.value as TransactionType | 'all')}
              >
                <MaterialIcons
                  name={filter.icon as any}
                  size={16}
                  color={selectedType === filter.value ? '#007AFF' : '#666'}
                />
                <Text style={[
                  styles.filterButtonText,
                  selectedType === filter.value && styles.filterButtonTextActive
                ]}>
                  {filter.label}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </Card>

        {/* Categories List */}
        {filteredCategories.length === 0 ? (
          <Card style={styles.emptyCard}>
            <View style={styles.emptyState}>
              <MaterialIcons name="category" size={48} color="#ccc" />
              <Text style={styles.emptyTitle}>
                {selectedType === 'all' ? 'No Categories Yet' : `No ${selectedType} Categories`}
              </Text>
              <Text style={styles.emptySubtitle}>
                Add categories to organize your transactions
              </Text>
              <Button
                title="Add Your First Category"
                onPress={handleAddCategory}
                style={styles.emptyButton}
              />
            </View>
          </Card>
        ) : (
          <View style={styles.categoriesGrid}>
            {filteredCategories.map((category) => (
              <CategoryCard
                key={category.value}
                category={category}
                onEdit={handleEditCategory}
                onDelete={handleDeleteCategory}
              />
            ))}
          </View>
        )}

        {/* Premium Upgrade Hint */}
        {subscription.tier === 'free' && categories.length >= 5 && (
          <Card style={styles.upgradeCard}>
            <View style={styles.upgradeContent}>
              <MaterialIcons name="star" size={24} color="#FFD700" />
              <View style={styles.upgradeText}>
                <Text style={styles.upgradeTitle}>Upgrade to Premium</Text>
                <Text style={styles.upgradeSubtitle}>
                  Get unlimited categories and advanced features
                </Text>
              </View>
            </View>
          </Card>
        )}
      </ScrollView>

      {/* Category Form Modal */}
      <Modal
        visible={isFormOpen}
        onClose={handleCloseForm}
        title={selectedCategory ? 'Edit Category' : 'Add Category'}
        fullScreen
      >
        <CategoryForm
          categoryToEdit={selectedCategory}
          onFinished={handleCloseForm}
        />
      </Modal>

      {/* Delete Confirmation Modal */}
      <Modal
        visible={isDeleteDialogOpen}
        onClose={() => setIsDeleteDialogOpen(false)}
        title="Delete Category"
      >
        <View style={styles.deleteDialog}>
          <Text style={styles.deleteMessage}>
            Are you sure you want to delete "{selectedCategory?.label}"?
          </Text>
          <Text style={styles.deleteWarning}>
            This action cannot be undone. Make sure this category has no transactions.
          </Text>
          <View style={styles.deleteButtons}>
            <Button
              title="Cancel"
              variant="outline"
              onPress={() => setIsDeleteDialogOpen(false)}
              style={styles.deleteButton}
            />
            <Button
              title="Delete"
              variant="danger"
              onPress={confirmDelete}
              style={styles.deleteButton}
            />
          </View>
        </View>
      </Modal>
    </View>
  );
}

interface CategoryCardProps {
  category: Category;
  onEdit: (category: Category) => void;
  onDelete: (category: Category) => void;
}

function CategoryCard({ category, onEdit, onDelete }: CategoryCardProps) {
  const getTypeColor = (type: TransactionType | 'all') => {
    switch (type) {
      case 'income': return '#4CAF50';
      case 'expense': return '#F44336';
      case 'all': return '#2196F3';
      default: return '#666';
    }
  };

  const getTypeIcon = (type: TransactionType | 'all') => {
    switch (type) {
      case 'income': return 'trending-up';
      case 'expense': return 'trending-down';
      case 'all': return 'list';
      default: return 'help';
    }
  };

  const handleLongPress = () => {
    Alert.alert(
      'Category Options',
      `What would you like to do with ${category.label}?`,
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Edit', onPress: () => onEdit(category) },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: () => onDelete(category)
        },
      ]
    );
  };

  return (
    <TouchableOpacity
      onPress={() => onEdit(category)}
      onLongPress={handleLongPress}
      activeOpacity={0.7}
      style={styles.categoryCard}
    >
      <Card style={styles.categoryCardInner}>
        <View style={styles.categoryHeader}>
          <View style={[
            styles.categoryIconContainer,
            { backgroundColor: `${getTypeColor(category.type)}20` }
          ]}>
            <MaterialIcons
              name="category"
              size={20}
              color={getTypeColor(category.type)}
            />
          </View>
          <View style={styles.categoryTypeIcon}>
            <MaterialIcons
              name={getTypeIcon(category.type) as any}
              size={14}
              color={getTypeColor(category.type)}
            />
          </View>
        </View>
        <Text style={styles.categoryName} numberOfLines={2}>
          {category.label}
        </Text>
        <Text style={[styles.categoryType, { color: getTypeColor(category.type) }]}>
          {category.type === 'all' ? 'All Types' : category.type}
        </Text>
      </Card>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    color: '#666',
  },
  scrollView: {
    flex: 1,
    padding: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  headerLeft: {
    flex: 1,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
  },
  headerSubtitle: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  addButton: {
    paddingHorizontal: 16,
  },
  filterCard: {
    marginBottom: 16,
  },
  filterTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 12,
  },
  filterButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  filterButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
    marginHorizontal: 4,
    borderRadius: 8,
    backgroundColor: '#f5f5f5',
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  filterButtonActive: {
    backgroundColor: '#E3F2FD',
    borderColor: '#007AFF',
  },
  filterButtonText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#666',
    marginLeft: 4,
  },
  filterButtonTextActive: {
    color: '#007AFF',
  },
  emptyCard: {
    paddingVertical: 40,
  },
  emptyState: {
    alignItems: 'center',
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginTop: 16,
  },
  emptySubtitle: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    marginTop: 8,
    marginBottom: 24,
  },
  emptyButton: {
    paddingHorizontal: 24,
  },
  categoriesGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginHorizontal: -6,
  },
  categoryCard: {
    width: '50%',
    paddingHorizontal: 6,
    marginBottom: 12,
  },
  categoryCardInner: {
    height: 120,
    justifyContent: 'space-between',
  },
  categoryHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  categoryIconContainer: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
  },
  categoryTypeIcon: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: '#f5f5f5',
    justifyContent: 'center',
    alignItems: 'center',
  },
  categoryName: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
    textAlign: 'center',
    marginVertical: 8,
  },
  categoryType: {
    fontSize: 12,
    fontWeight: '500',
    textAlign: 'center',
    textTransform: 'capitalize',
  },
  upgradeCard: {
    marginTop: 16,
    backgroundColor: '#FFF9E6',
    borderColor: '#FFD700',
    borderWidth: 1,
  },
  upgradeContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  upgradeText: {
    flex: 1,
    marginLeft: 12,
  },
  upgradeTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 2,
  },
  upgradeSubtitle: {
    fontSize: 14,
    color: '#666',
  },
  deleteDialog: {
    padding: 8,
  },
  deleteMessage: {
    fontSize: 16,
    color: '#333',
    marginBottom: 12,
    textAlign: 'center',
  },
  deleteWarning: {
    fontSize: 14,
    color: '#666',
    marginBottom: 24,
    textAlign: 'center',
  },
  deleteButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  deleteButton: {
    flex: 0.48,
  },
});

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginTop: 16,
  },
  subtitle: {
    fontSize: 18,
    color: '#666',
    marginTop: 8,
  },
  description: {
    fontSize: 16,
    color: '#999',
    textAlign: 'center',
    marginTop: 16,
    lineHeight: 24,
  },
});
