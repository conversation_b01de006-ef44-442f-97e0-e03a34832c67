import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';

import { useTransactions } from '../hooks/useTransactions';
import { CATEGORY_ICONS } from '../lib/constants';
import Input from './ui/Input';
import Button from './ui/Button';
import Card from './ui/Card';
import type { Category, CategoryInfo, TransactionType } from '../lib/types';

const categorySchema = z.object({
  value: z.string().min(1, 'Category ID is required'),
  label: z.string().min(1, 'Category name is required'),
  iconName: z.string().min(1, 'Please select an icon'),
  type: z.enum(['income', 'expense', 'all']),
});

interface CategoryFormProps {
  categoryToEdit?: Category;
  onFinished: () => void;
}

export default function CategoryForm({ categoryToEdit, onFinished }: CategoryFormProps) {
  const { addCategory, editCategory } = useTransactions();
  const [loading, setLoading] = useState(false);

  const {
    control,
    handleSubmit,
    setValue,
    watch,
    reset,
    formState: { errors },
  } = useForm<CategoryInfo>({
    resolver: zodResolver(categorySchema),
    defaultValues: {
      value: '',
      label: '',
      iconName: CATEGORY_ICONS[0].name,
      type: 'expense',
    },
  });

  const selectedIcon = watch('iconName');
  const selectedType = watch('type');
  const categoryLabel = watch('label');

  useEffect(() => {
    if (categoryToEdit) {
      reset({
        value: categoryToEdit.value,
        label: categoryToEdit.label,
        iconName: categoryToEdit.iconName,
        type: categoryToEdit.type,
      });
    }
  }, [categoryToEdit, reset]);

  // Auto-generate value from label
  useEffect(() => {
    if (!categoryToEdit && categoryLabel) {
      const generatedValue = categoryLabel
        .toLowerCase()
        .replace(/[^a-z0-9]/g, '-')
        .replace(/-+/g, '-')
        .replace(/^-|-$/g, '');
      setValue('value', generatedValue);
    }
  }, [categoryLabel, categoryToEdit, setValue]);

  const onSubmit = async (data: CategoryInfo) => {
    setLoading(true);
    try {
      if (categoryToEdit) {
        await editCategory(categoryToEdit.value, data);
      } else {
        await addCategory(data);
      }
      onFinished();
    } catch (error) {
      Alert.alert('Error', 'Failed to save category');
    } finally {
      setLoading(false);
    }
  };

  const typeOptions = [
    { value: 'expense', label: 'Expense', icon: 'trending-down', color: '#F44336' },
    { value: 'income', label: 'Income', icon: 'trending-up', color: '#4CAF50' },
    { value: 'all', label: 'Both', icon: 'list', color: '#2196F3' },
  ];

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      {/* Category Name */}
      <Card style={styles.section}>
        <Controller
          control={control}
          name="label"
          render={({ field: { onChange, value } }) => (
            <Input
              label="Category Name"
              placeholder="e.g., Groceries, Salary, Entertainment"
              value={value}
              onChangeText={onChange}
              error={errors.label?.message}
              icon="label"
            />
          )}
        />
      </Card>

      {/* Category ID (auto-generated) */}
      <Card style={styles.section}>
        <Controller
          control={control}
          name="value"
          render={({ field: { onChange, value } }) => (
            <Input
              label="Category ID"
              placeholder="auto-generated-id"
              value={value}
              onChangeText={onChange}
              error={errors.value?.message}
              icon="code"
              editable={!!categoryToEdit}
            />
          )}
        />
        {!categoryToEdit && (
          <Text style={styles.helperText}>
            This is auto-generated from the category name
          </Text>
        )}
      </Card>

      {/* Transaction Type */}
      <Card style={styles.section}>
        <Text style={styles.sectionTitle}>Transaction Type</Text>
        <Controller
          control={control}
          name="type"
          render={({ field: { onChange, value } }) => (
            <View style={styles.typeContainer}>
              {typeOptions.map((option) => (
                <TouchableOpacity
                  key={option.value}
                  style={[
                    styles.typeButton,
                    value === option.value && [
                      styles.typeButtonActive,
                      { borderColor: option.color }
                    ]
                  ]}
                  onPress={() => onChange(option.value)}
                >
                  <MaterialIcons 
                    name={option.icon as any} 
                    size={20} 
                    color={value === option.value ? option.color : '#666'} 
                  />
                  <Text style={[
                    styles.typeButtonText,
                    value === option.value && { color: option.color }
                  ]}>
                    {option.label}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          )}
        />
      </Card>

      {/* Icon Selection */}
      <Card style={styles.section}>
        <Text style={styles.sectionTitle}>Choose an Icon</Text>
        <Controller
          control={control}
          name="iconName"
          render={({ field: { onChange, value } }) => (
            <View style={styles.iconGrid}>
              {CATEGORY_ICONS.map((iconInfo) => {
                const IconComponent = iconInfo.icon;
                const isSelected = value === iconInfo.name;
                
                return (
                  <TouchableOpacity
                    key={iconInfo.name}
                    style={[
                      styles.iconButton,
                      isSelected && styles.iconButtonSelected
                    ]}
                    onPress={() => onChange(iconInfo.name)}
                  >
                    <IconComponent 
                      name={iconInfo.name as any} 
                      size={20} 
                      color={isSelected ? '#007AFF' : '#666'} 
                    />
                  </TouchableOpacity>
                );
              })}
            </View>
          )}
        />
        {errors.iconName && (
          <Text style={styles.errorText}>{errors.iconName.message}</Text>
        )}
      </Card>

      {/* Preview */}
      <Card style={styles.section}>
        <Text style={styles.sectionTitle}>Preview</Text>
        <View style={styles.preview}>
          <View style={[
            styles.previewIconContainer,
            { backgroundColor: `${typeOptions.find(t => t.value === selectedType)?.color || '#666'}20` }
          ]}>
            {(() => {
              const iconInfo = CATEGORY_ICONS.find(i => i.name === selectedIcon);
              const IconComponent = iconInfo?.icon || MaterialIcons;
              return (
                <IconComponent 
                  name={selectedIcon as any} 
                  size={24} 
                  color={typeOptions.find(t => t.value === selectedType)?.color || '#666'} 
                />
              );
            })()}
          </View>
          <View style={styles.previewInfo}>
            <Text style={styles.previewName}>
              {categoryLabel || 'Category Name'}
            </Text>
            <Text style={[
              styles.previewType,
              { color: typeOptions.find(t => t.value === selectedType)?.color || '#666' }
            ]}>
              {typeOptions.find(t => t.value === selectedType)?.label || 'Type'}
            </Text>
          </View>
        </View>
      </Card>

      {/* Submit Button */}
      <View style={styles.buttonContainer}>
        <Button
          title={categoryToEdit ? 'Update Category' : 'Create Category'}
          onPress={handleSubmit(onSubmit)}
          loading={loading}
          size="large"
        />
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  section: {
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 12,
  },
  helperText: {
    fontSize: 12,
    color: '#666',
    marginTop: 4,
    fontStyle: 'italic',
  },
  typeContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  typeButton: {
    flex: 1,
    alignItems: 'center',
    paddingVertical: 12,
    marginHorizontal: 4,
    borderRadius: 8,
    borderWidth: 2,
    borderColor: '#e0e0e0',
    backgroundColor: '#f9f9f9',
  },
  typeButtonActive: {
    backgroundColor: '#fff',
    borderWidth: 2,
  },
  typeButtonText: {
    fontSize: 12,
    fontWeight: '500',
    color: '#666',
    marginTop: 4,
  },
  iconGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginHorizontal: -4,
  },
  iconButton: {
    width: 48,
    height: 48,
    margin: 4,
    borderRadius: 24,
    backgroundColor: '#f5f5f5',
    borderWidth: 2,
    borderColor: '#e0e0e0',
    justifyContent: 'center',
    alignItems: 'center',
  },
  iconButtonSelected: {
    backgroundColor: '#E3F2FD',
    borderColor: '#007AFF',
  },
  errorText: {
    fontSize: 14,
    color: '#FF3B30',
    marginTop: 8,
  },
  preview: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#f9f9f9',
    borderRadius: 8,
  },
  previewIconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  previewInfo: {
    flex: 1,
  },
  previewName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 4,
  },
  previewType: {
    fontSize: 14,
    fontWeight: '500',
    textTransform: 'capitalize',
  },
  buttonContainer: {
    paddingVertical: 20,
  },
});
