import React, { useState, useMemo } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useTransactions } from '../hooks/useTransactions';
import { formatCurrency } from '../lib/utils';
import { WALLET_ICONS } from '../lib/constants';
import Card from '../components/ui/Card';
import Button from '../components/ui/Button';
import Modal from '../components/ui/Modal';
import WalletForm from '../components/WalletForm';
import type { Wallet } from '../lib/types';

export default function WalletsScreen() {
  const { wallets, isInitialized, deleteWallet, subscription } = useTransactions();
  const [selectedWallet, setSelectedWallet] = useState<Wallet | undefined>();
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);

  const totalBalance = useMemo(() => {
    return wallets.reduce((sum, wallet) => sum + wallet.balance, 0);
  }, [wallets]);

  const handleAddWallet = () => {
    setSelectedWallet(undefined);
    setIsFormOpen(true);
  };

  const handleEditWallet = (wallet: Wallet) => {
    setSelectedWallet(wallet);
    setIsFormOpen(true);
  };

  const handleDeleteWallet = (wallet: Wallet) => {
    setSelectedWallet(wallet);
    setIsDeleteDialogOpen(true);
  };

  const confirmDelete = async () => {
    if (selectedWallet) {
      await deleteWallet(selectedWallet.id);
      setIsDeleteDialogOpen(false);
      setSelectedWallet(undefined);
    }
  };

  const handleCloseForm = () => {
    setIsFormOpen(false);
    setSelectedWallet(undefined);
  };

  if (!isInitialized) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#007AFF" />
        <Text style={styles.loadingText}>Loading wallets...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Total Balance Card */}
        <Card style={styles.totalCard}>
          <View style={styles.totalHeader}>
            <MaterialIcons name="account-balance" size={24} color="#007AFF" />
            <Text style={styles.totalTitle}>Total Balance</Text>
          </View>
          <Text style={styles.totalAmount}>{formatCurrency(totalBalance)}</Text>
          <Text style={styles.totalSubtitle}>
            Across {wallets.length} wallet{wallets.length !== 1 ? 's' : ''}
          </Text>
        </Card>

        {/* Wallets List */}
        <View style={styles.walletsSection}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Your Wallets</Text>
            <Button
              title="Add Wallet"
              onPress={handleAddWallet}
              size="small"
              style={styles.addButton}
            />
          </View>

          {wallets.length === 0 ? (
            <Card style={styles.emptyCard}>
              <View style={styles.emptyState}>
                <MaterialIcons name="account-balance-wallet" size={48} color="#ccc" />
                <Text style={styles.emptyTitle}>No Wallets Yet</Text>
                <Text style={styles.emptySubtitle}>
                  Add your first wallet to start tracking your finances
                </Text>
                <Button
                  title="Add Your First Wallet"
                  onPress={handleAddWallet}
                  style={styles.emptyButton}
                />
              </View>
            </Card>
          ) : (
            wallets.map((wallet) => (
              <WalletCard
                key={wallet.id}
                wallet={wallet}
                onEdit={handleEditWallet}
                onDelete={handleDeleteWallet}
              />
            ))
          )}
        </View>

        {/* Premium Upgrade Hint */}
        {subscription.tier === 'free' && wallets.length >= 3 && (
          <Card style={styles.upgradeCard}>
            <View style={styles.upgradeContent}>
              <MaterialIcons name="star" size={24} color="#FFD700" />
              <View style={styles.upgradeText}>
                <Text style={styles.upgradeTitle}>Upgrade to Premium</Text>
                <Text style={styles.upgradeSubtitle}>
                  Get unlimited wallets and advanced features
                </Text>
              </View>
            </View>
          </Card>
        )}
      </ScrollView>

      {/* Wallet Form Modal */}
      <Modal
        visible={isFormOpen}
        onClose={handleCloseForm}
        title={selectedWallet ? 'Edit Wallet' : 'Add Wallet'}
        fullScreen
      >
        <WalletForm
          walletToEdit={selectedWallet}
          onFinished={handleCloseForm}
        />
      </Modal>

      {/* Delete Confirmation Modal */}
      <Modal
        visible={isDeleteDialogOpen}
        onClose={() => setIsDeleteDialogOpen(false)}
        title="Delete Wallet"
      >
        <View style={styles.deleteDialog}>
          <Text style={styles.deleteMessage}>
            Are you sure you want to delete "{selectedWallet?.name}"?
          </Text>
          <Text style={styles.deleteWarning}>
            This action cannot be undone. Make sure this wallet has no transactions.
          </Text>
          <View style={styles.deleteButtons}>
            <Button
              title="Cancel"
              variant="outline"
              onPress={() => setIsDeleteDialogOpen(false)}
              style={styles.deleteButton}
            />
            <Button
              title="Delete"
              variant="danger"
              onPress={confirmDelete}
              style={styles.deleteButton}
            />
          </View>
        </View>
      </Modal>
    </View>
  );
}

interface WalletCardProps {
  wallet: Wallet;
  onEdit: (wallet: Wallet) => void;
  onDelete: (wallet: Wallet) => void;
}

function WalletCard({ wallet, onEdit, onDelete }: WalletCardProps) {
  const walletIcon = WALLET_ICONS.find(icon => icon.name === wallet.icon);
  const IconComponent = walletIcon?.icon || MaterialIcons;

  const handleLongPress = () => {
    Alert.alert(
      'Wallet Options',
      `What would you like to do with ${wallet.name}?`,
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Edit', onPress: () => onEdit(wallet) },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: () => onDelete(wallet)
        },
      ]
    );
  };

  return (
    <TouchableOpacity
      onPress={() => onEdit(wallet)}
      onLongPress={handleLongPress}
      activeOpacity={0.7}
    >
      <Card style={styles.walletCard}>
        <View style={styles.walletHeader}>
          <View style={styles.walletIconContainer}>
            <IconComponent
              name={wallet.icon as any}
              size={24}
              color="#007AFF"
            />
          </View>
          <View style={styles.walletInfo}>
            <Text style={styles.walletName}>{wallet.name}</Text>
            <Text style={styles.walletCurrency}>{wallet.currency}</Text>
          </View>
          <View style={styles.walletActions}>
            <TouchableOpacity
              onPress={() => onEdit(wallet)}
              style={styles.actionButton}
            >
              <MaterialIcons name="edit" size={20} color="#666" />
            </TouchableOpacity>
            <TouchableOpacity
              onPress={() => onDelete(wallet)}
              style={styles.actionButton}
            >
              <MaterialIcons name="delete" size={20} color="#FF3B30" />
            </TouchableOpacity>
          </View>
        </View>
        <View style={styles.walletBalance}>
          <Text style={styles.balanceAmount}>
            {formatCurrency(wallet.balance, wallet.currency)}
          </Text>
          <Text style={[
            styles.balanceStatus,
            { color: wallet.balance >= 0 ? '#4CAF50' : '#FF3B30' }
          ]}>
            {wallet.balance >= 0 ? 'Positive' : 'Negative'} Balance
          </Text>
        </View>
      </Card>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    color: '#666',
  },
  scrollView: {
    flex: 1,
    padding: 16,
  },
  totalCard: {
    marginBottom: 24,
    alignItems: 'center',
  },
  totalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  totalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginLeft: 8,
  },
  totalAmount: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#007AFF',
    marginBottom: 4,
  },
  totalSubtitle: {
    fontSize: 14,
    color: '#666',
  },
  walletsSection: {
    flex: 1,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#333',
  },
  addButton: {
    paddingHorizontal: 16,
  },
  emptyCard: {
    paddingVertical: 40,
  },
  emptyState: {
    alignItems: 'center',
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginTop: 16,
  },
  emptySubtitle: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    marginTop: 8,
    marginBottom: 24,
  },
  emptyButton: {
    paddingHorizontal: 24,
  },
  walletCard: {
    marginBottom: 12,
  },
  walletHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  walletIconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: '#E3F2FD',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  walletInfo: {
    flex: 1,
  },
  walletName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 2,
  },
  walletCurrency: {
    fontSize: 12,
    color: '#666',
  },
  walletActions: {
    flexDirection: 'row',
  },
  actionButton: {
    padding: 8,
    marginLeft: 4,
  },
  walletBalance: {
    alignItems: 'center',
  },
  balanceAmount: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  balanceStatus: {
    fontSize: 12,
    fontWeight: '500',
  },
  upgradeCard: {
    marginTop: 16,
    backgroundColor: '#FFF9E6',
    borderColor: '#FFD700',
    borderWidth: 1,
  },
  upgradeContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  upgradeText: {
    flex: 1,
    marginLeft: 12,
  },
  upgradeTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 2,
  },
  upgradeSubtitle: {
    fontSize: 14,
    color: '#666',
  },
  deleteDialog: {
    padding: 8,
  },
  deleteMessage: {
    fontSize: 16,
    color: '#333',
    marginBottom: 12,
    textAlign: 'center',
  },
  deleteWarning: {
    fontSize: 14,
    color: '#666',
    marginBottom: 24,
    textAlign: 'center',
  },
  deleteButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  deleteButton: {
    flex: 0.48,
  },
});

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginTop: 16,
  },
  subtitle: {
    fontSize: 18,
    color: '#666',
    marginTop: 8,
  },
  description: {
    fontSize: 16,
    color: '#999',
    textAlign: 'center',
    marginTop: 16,
    lineHeight: 24,
  },
});
